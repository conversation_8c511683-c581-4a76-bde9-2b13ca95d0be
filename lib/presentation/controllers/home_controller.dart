import 'package:get/get.dart';

import '../../core/utils/logger.dart';
import '../../domain/entities/ad.dart';
import '../../domain/entities/trip.dart';
import '../../domain/usecases/get_ads.dart';
import '../../domain/usecases/get_current_trip.dart';
import '../../domain/usecases/get_profile.dart';
import '../routes/app_routes.dart';

/// HomeController class for managing home screen state
/// Following Single Responsibility Principle by focusing only on home screen functionality
class HomeController extends GetxController {
  // Use cases
  final GetAds? getAdsUseCase;
  final GetCurrentTrip? getCurrentTripUseCase;
  final GetProfile? getProfileUseCase;

  // Observable state
  final RxInt _selectedIndex = 0.obs;
  final _isLoading = true.obs;
  final _errorMessage = ''.obs;
  final _currentTrip = Rx<Trip?>(null);
  final _ads = <Ad>[].obs;
  final _userName = ''.obs;
  final _userAddress = ''.obs;

  // Getters
  int get selectedIndex => _selectedIndex.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  Trip? get currentTrip => _currentTrip.value;
  List<Ad> get ads => _ads;
  String get userName => _userName.value;
  String get userAddress => _userAddress.value;
  bool get hasCurrentTrip => _currentTrip.value != null;
  bool get hasAds => _ads.isNotEmpty;

  HomeController({
    this.getAdsUseCase,
    this.getCurrentTripUseCase,
    this.getProfileUseCase,
  });

  @override
  void onInit() {
    super.onInit();
    loadHomeData();
  }

  /// Change the selected tab
  void changeTab(int index) {
    _selectedIndex.value = index;
  }

  /// Load home data
  Future<void> loadHomeData() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // Load profile data
      await _loadProfileData();

      // Load current trip
      await _loadCurrentTrip();

      // Load ads
      await _loadAds();
    } catch (e) {
      LoggerService.error('Error loading home data', error: e);
      _errorMessage.value = 'Failed to load home data';
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load profile data
  Future<void> _loadProfileData() async {
    if (getProfileUseCase == null) return;

    final result = await getProfileUseCase!();

    result.fold(
      (failure) {
        LoggerService.error('Error loading profile data', error: failure);
        _errorMessage.value = failure.message;
      },
      (profile) {
        _userName.value = profile.name;
        _userAddress.value = profile.address ?? '';
      },
    );
  }

  /// Load current trip
  Future<void> _loadCurrentTrip() async {
    if (getCurrentTripUseCase == null) return;

    final result = await getCurrentTripUseCase!();

    result.fold(
      (failure) {
        LoggerService.error('Error loading current trip', error: failure);
        _errorMessage.value = failure.message;
      },
      (trip) {
        _currentTrip.value = trip;
      },
    );
  }

  /// Load ads - using local images temporarily
  Future<void> _loadAds() async {
    // Instead of calling API, use local mock data
    LoggerService.debug('Loading local mock ads data');

    // Create mock ads with local placeholder images
    final mockAds = [
      Ad(
        id: 1,
        title: 'مرحبًا بكم في بساطي',
        description: 'تطبيق إدارة النقل المدرسي الأفضل',
        imageUrl: '', // Empty URL to trigger local placeholder
        link: 'https://busaty.com',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isActive: true,
      ),
      Ad(
        id: 2,
        title: 'تتبع رحلات الطلاب',
        description: 'تابع رحلات أبنائك بسهولة وأمان',
        imageUrl: '', // Empty URL to trigger local placeholder
        link: 'https://busaty.com/features',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isActive: true,
      ),
      Ad(
        id: 3,
        title: 'إدارة الحافلات المدرسية',
        description: 'إدارة متكاملة للحافلات والسائقين والمشرفين',
        imageUrl: '', // Empty URL to trigger local placeholder
        link: 'https://busaty.com/management',
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 30)),
        isActive: true,
      ),
    ];

    // Assign mock ads to the observable list
    _ads.assignAll(mockAds);

    LoggerService.debug('Loaded ${mockAds.length} mock ads');
  }

  /// Refresh home data
  Future<void> refreshHomeData() async {
    await loadHomeData();
  }

  /// Navigate to current trip
  void navigateToCurrentTrip() {
    Get.toNamed(AppRoutes.trackTrip);
  }

  /// Navigate to students
  void navigateToStudents() {
    Get.toNamed(AppRoutes.students);
  }

  /// Navigate to supervisors
  void navigateToSupervisors() {
    Get.toNamed(AppRoutes.supervisors);
  }

  /// Navigate to buses
  void navigateToBuses() {
    Get.toNamed(AppRoutes.buses);
  }

  /// Navigate to drivers
  void navigateToDrivers() {
    Get.toNamed(AppRoutes.drivers);
  }

  /// Navigate to address change requests
  void navigateToAddressChangeRequests() {
    Get.toNamed(AppRoutes.addressChangeRequests);
  }

  /// Navigate to previous trips
  void navigateToPreviousTrips() {
    Get.toNamed(AppRoutes.previousTrips);
  }
}
