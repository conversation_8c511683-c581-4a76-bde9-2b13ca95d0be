import 'package:flutter/material.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../controllers/home_controller.dart';

/// HomeGrid widget for displaying main navigation options
/// Following Single Responsibility Principle by focusing only on grid UI
class HomeGrid extends StatelessWidget {
  final HomeController controller;

  const HomeGrid({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final isDesktop =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.desktop;
    final isTablet =
        ResponsiveUtils.getDeviceType(context) == DeviceScreenType.tablet;

    // Define grid items
    final List<_GridItem> gridItems = [
      _GridItem(
        icon: Icons.people_alt_rounded,
        title: 'الطلاب',
        onTap: controller.navigateToStudents,
        color: ColorConstants.primary,
      ),
      _GridItem(
        icon: Icons.supervisor_account_rounded,
        title: 'المشرفين',
        onTap: controller.navigateToSupervisors,
        color: ColorConstants.secondary,
      ),
      _GridItem(
        icon: Icons.directions_bus_rounded,
        title: 'الحافلات',
        onTap: controller.navigateToBuses,
        color: ColorConstants.accent1,
      ),
      _GridItem(
        icon: Icons.drive_eta_rounded,
        title: 'السائقين',
        onTap: controller.navigateToDrivers,
        color: ColorConstants.accent2,
      ),

      _GridItem(
        icon: Icons.location_on_rounded,
        title: 'طلبات تغيير العنوان',
        onTap: controller.navigateToAddressChangeRequests,
        color: ColorConstants.secondary,
      ),
      _GridItem(
        icon: Icons.history_rounded,
        title: 'الرحلات السابقة',
        onTap: controller.navigateToPreviousTrips,
        color: ColorConstants.accent1,
      ),
    ];

    // Calculate number of columns based on screen size
    int crossAxisCount = isDesktop ? 4 : (isTablet ? 3 : 2);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: gridItems.length,
      itemBuilder: (context, index) {
        final item = gridItems[index];
        return _buildGridItem(context, item);
      },
    );
  }

  Widget _buildGridItem(BuildContext context, _GridItem item) {
    return _EnhancedGridItem(item: item);
  }
}

/// Enhanced grid item with animations and modern design
class _EnhancedGridItem extends StatefulWidget {
  final _GridItem item;

  const _EnhancedGridItem({required this.item});

  @override
  State<_EnhancedGridItem> createState() => _EnhancedGridItemState();
}

class _EnhancedGridItemState extends State<_EnhancedGridItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 0.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: MouseRegion(
              onEnter: (_) {
                setState(() => _isHovered = true);
                _animationController.forward();
              },
              onExit: (_) {
                setState(() => _isHovered = false);
                _animationController.reverse();
              },
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      widget.item.color.withValues(alpha: 0.02),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: widget.item.color.withValues(
                        alpha: _isHovered ? 0.15 : 0.08,
                      ),
                      blurRadius: _isHovered ? 20 : 10,
                      offset: const Offset(0, 8),
                      spreadRadius: _isHovered ? 2 : 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.04),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color:
                        _isHovered
                            ? widget.item.color.withValues(alpha: 0.3)
                            : widget.item.color.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: widget.item.onTap,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Enhanced icon with gradient background
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  widget.item.color.withValues(alpha: 0.15),
                                  widget.item.color.withValues(alpha: 0.08),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: widget.item.color.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Icon(
                              widget.item.icon,
                              color: widget.item.color,
                              size: ResponsiveUtils.getResponsiveIconSize(
                                context,
                                mobile: 22,
                                tablet: 26,
                                desktop: 30,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // Enhanced title
                          Text(
                            widget.item.title,
                            style: TextStyle(
                              fontSize:
                                  ResponsiveUtils.getResponsiveFontSizeByDevice(
                                    context,
                                    mobile: 12,
                                    tablet: 14,
                                    desktop: 16,
                                  ),
                              fontWeight: FontWeight.w700,
                              color: ColorConstants.textPrimary,
                              letterSpacing: 0.2,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Grid item model
class _GridItem {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color color;

  _GridItem({
    required this.icon,
    required this.title,
    required this.onTap,
    required this.color,
  });
}
