import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../domain/entities/parent.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../routes/app_routes.dart';

/// Parent card widget for displaying parent information
/// Following Single Responsibility Principle by focusing only on parent card UI
class ParentCard extends StatelessWidget {
  final Parent parent;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onViewDetails;

  const ParentCard({
    super.key,
    required this.parent,
    this.onEdit,
    this.onDelete,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? ColorConstants.surfaceDark
                : ColorConstants.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.textSecondaryDark.withValues(alpha: 0.3)
                  : ColorConstants.textSecondary.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap:
            onViewDetails ??
            () => Get.toNamed(AppRoutes.parentDetails, arguments: parent),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: isMobile ? _buildMobileLayout() : _buildDesktopLayout(),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 12),
        _buildContactInfo(),
        const SizedBox(height: 12),
        _buildStatusAndActions(),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        _buildAvatar(),
        const SizedBox(width: 16),
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNameAndStatus(),
              const SizedBox(height: 8),
              _buildContactInfo(),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(flex: 2, child: _buildChildrenInfo()),
        const SizedBox(width: 16),
        _buildActions(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildAvatar(),
        const SizedBox(width: 12),
        Expanded(child: _buildNameAndStatus()),
      ],
    );
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 24,
      backgroundColor: ColorConstants.primary.withValues(alpha: 0.1),
      child: Text(
        parent.name?.isNotEmpty == true ? parent.name![0].toUpperCase() : 'P',
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: ColorConstants.primary,
        ),
      ),
    );
  }

  Widget _buildNameAndStatus() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          parent.name ?? 'غير محدد',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        _buildStatusChip(),
      ],
    );
  }

  Widget _buildStatusChip() {
    final isActive = parent.status == 1;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color:
            isActive
                ? ColorConstants.success.withValues(alpha: 0.1)
                : ColorConstants.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? ColorConstants.success : ColorConstants.error,
          width: 1,
        ),
      ),
      child: Text(
        isActive ? 'نشط' : 'غير نشط',
        style: TextStyle(
          color: isActive ? ColorConstants.success : ColorConstants.error,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildContactInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (parent.email?.isNotEmpty == true) ...[
          Row(
            children: [
              Icon(Icons.email, size: 16, color: ColorConstants.textSecondary),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  parent.email!,
                  style: TextStyle(
                    color: ColorConstants.textSecondary,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
        ],
        if (parent.phone?.isNotEmpty == true) ...[
          Row(
            children: [
              Icon(Icons.phone, size: 16, color: ColorConstants.textSecondary),
              const SizedBox(width: 8),
              Text(
                parent.phone!,
                style: TextStyle(
                  color: ColorConstants.textSecondary,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildChildrenInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ColorConstants.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ColorConstants.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.child_care, size: 16, color: ColorConstants.primary),
              const SizedBox(width: 8),
              Text(
                'الأطفال',
                style: TextStyle(
                  color: ColorConstants.primary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${parent.childrenCount ?? 0} طفل',
            style: TextStyle(color: ColorConstants.textSecondary, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusAndActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [_buildChildrenInfo(), _buildActions()],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed:
              onViewDetails ??
              () => Get.toNamed(AppRoutes.parentDetails, arguments: parent),
          icon: const Icon(Icons.visibility),
          tooltip: 'عرض التفاصيل',
          iconSize: 20,
        ),
        IconButton(
          onPressed:
              onEdit ??
              () => Get.toNamed(AppRoutes.editParent, arguments: parent),
          icon: const Icon(Icons.edit),
          tooltip: 'تعديل',
          iconSize: 20,
        ),
        IconButton(
          onPressed: onDelete,
          icon: const Icon(Icons.delete),
          tooltip: 'حذف',
          iconSize: 20,
          color: ColorConstants.error,
        ),
      ],
    );
  }
}
